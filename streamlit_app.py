#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问 + PandasAI Streamlit 前端界面
支持连续对话、文档上传、持久化存储
"""

import streamlit as st
import pandas as pd
import os
import json
import uuid
from datetime import datetime
from pathlib import Path
import shutil
from dotenv import load_dotenv
import traceback

# 导入项目核心功能
CORE_MODULES_AVAILABLE = True
try:
    from perfect_tongyi_integration import TongyiQianwenLLM, analyze_data
    from working_tongyi_integration import create_smart_dataframe_with_tongyi
    from result_formatter import EnhancedResultFormatter
    from metadata_ui import MetadataUI
    from metadata_manager import metadata_manager
    print("✅ 核心模块导入成功")
except ImportError as e:
    CORE_MODULES_AVAILABLE = False
    st.error("❌ 导入核心模块失败")
    st.error(f"错误详情: {e}")

def fix_chart_generation_error(result, df):
    """修复图表生成错误的备用方案"""
    if result.get('error') and 'expected an indented block' in result['error']:
        st.warning("🔧 检测到代码生成问题，使用备用图表方案")

        try:
            # 多维度分析备用方案
            grouped_data = df.groupby(['地区', '产品名称'])['销售额'].sum().reset_index()

            st.subheader("📊 各地区产品销售分析")
            st.dataframe(grouped_data, use_container_width=True)

            # 为每个地区生成图表
            regions = grouped_data['地区'].unique()

            # 创建列布局
            cols = st.columns(2)

            for i, region in enumerate(regions):
                region_data = grouped_data[grouped_data['地区'] == region]
                if not region_data.empty:
                    with cols[i % 2]:
                        st.subheader(f"📈 {region}地区产品销售分布")

                        # 使用Streamlit原生图表
                        chart_data = region_data.set_index('产品名称')['销售额']
                        st.bar_chart(chart_data)

                        # 显示具体数据
                        st.caption("具体数据:")
                        for product, amount in chart_data.items():
                            st.write(f"• {product}: ¥{amount:,.0f}")

            st.success("✅ 备用图表方案执行成功")
            return True

        except Exception as e:
            st.error(f"❌ 备用方案也失败了: {e}")
            return False

    return False

def enhanced_analyze_with_fallback(df, query):
    """增强的分析函数，包含错误恢复"""
    try:
        # 尝试正常分析
        result = analyze_data(df, query, "sales_data", use_metadata=True)

        # 检查是否有图表生成错误
        if fix_chart_generation_error(result, df):
            return {"success": True, "fallback_used": True, "result": result}

        # 正常显示结果
        return {"success": True, "fallback_used": False, "result": result}

    except Exception as e:
        st.error(f"❌ 分析失败: {e}")
        return {"success": False, "error": str(e)}

    st.markdown("### 🔧 解决方案")
    st.markdown("""
    **可能的原因和解决方法：**

    1. **虚拟环境问题**
       - 确保在正确的虚拟环境中运行
       - 运行: `venv\\Scripts\\activate` (Windows) 或 `source venv/bin/activate` (Linux/Mac)

    2. **依赖未安装**
       - 运行: `pip install pandasai==2.2.15 PyYAML`
       - 或使用: `pip install -r requirements_streamlit.txt`

    3. **版本兼容性问题**
       - 尝试: `pip install numpy==1.24.3 pandas==1.5.3 --force-reinstall`

    4. **使用启动脚本**
       - Windows: 双击 `start_app.bat`
       - 或运行: `python run_streamlit.py`
    """)

    st.info("💡 建议使用提供的启动脚本来自动处理环境和依赖问题")
    st.stop()

# 加载环境变量
load_dotenv()

# 页面配置
st.set_page_config(
    page_title="智能数据分析助手",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 全局配置
UPLOAD_DIR = Path("uploaded_files")
CHAT_HISTORY_DIR = Path("chat_history")
SUPPORTED_FORMATS = ['.csv', '.xlsx', '.xls', '.txt', '.json']

# 创建必要目录
UPLOAD_DIR.mkdir(exist_ok=True)
CHAT_HISTORY_DIR.mkdir(exist_ok=True)

class ChatManager:
    """聊天会话管理器"""
    
    def __init__(self):
        self.session_id = self._get_or_create_session_id()
        self.history_file = CHAT_HISTORY_DIR / f"chat_{self.session_id}.json"
    
    def _get_or_create_session_id(self):
        """获取或创建会话ID"""
        if 'session_id' not in st.session_state:
            st.session_state.session_id = str(uuid.uuid4())
        return st.session_state.session_id
    
    def load_chat_history(self):
        """加载聊天历史"""
        if self.history_file.exists():
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                st.error(f"加载聊天历史失败: {e}")
        return []
    
    def save_chat_history(self, history):
        """保存聊天历史"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            st.error(f"保存聊天历史失败: {e}")
    
    def add_message(self, role, content, data_info=None):
        """添加消息到历史"""
        if 'chat_history' not in st.session_state:
            st.session_state.chat_history = self.load_chat_history()
        
        message = {
            'role': role,
            'content': content,
            'timestamp': datetime.now().isoformat(),
            'data_info': data_info
        }
        
        st.session_state.chat_history.append(message)
        self.save_chat_history(st.session_state.chat_history)

class FileManager:
    """文件管理器"""
    
    @staticmethod
    def save_uploaded_file(uploaded_file):
        """保存上传的文件"""
        try:
            file_path = UPLOAD_DIR / uploaded_file.name
            with open(file_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            return file_path
        except Exception as e:
            st.error(f"文件保存失败: {e}")
            return None
    
    @staticmethod
    def load_data_file(file_path):
        """加载数据文件"""
        try:
            file_ext = file_path.suffix.lower()
            
            if file_ext == '.csv':
                return pd.read_csv(file_path, encoding='utf-8')
            elif file_ext in ['.xlsx', '.xls']:
                return pd.read_excel(file_path)
            elif file_ext == '.json':
                return pd.read_json(file_path)
            elif file_ext == '.txt':
                # 尝试作为CSV读取
                return pd.read_csv(file_path, sep='\t', encoding='utf-8')
            else:
                st.error(f"不支持的文件格式: {file_ext}")
                return None
                
        except Exception as e:
            st.error(f"文件加载失败: {e}")
            return None
    
    @staticmethod
    def get_uploaded_files():
        """获取已上传的文件列表"""
        if not UPLOAD_DIR.exists():
            return []
        
        files = []
        for file_path in UPLOAD_DIR.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in SUPPORTED_FORMATS:
                files.append({
                    'name': file_path.name,
                    'path': file_path,
                    'size': file_path.stat().st_size,
                    'modified': datetime.fromtimestamp(file_path.stat().st_mtime)
                })
        
        return sorted(files, key=lambda x: x['modified'], reverse=True)

def init_session_state():
    """初始化会话状态"""
    if 'chat_manager' not in st.session_state:
        st.session_state.chat_manager = ChatManager()

    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = st.session_state.chat_manager.load_chat_history()

    if 'current_data' not in st.session_state:
        st.session_state.current_data = None

    if 'llm' not in st.session_state:
        st.session_state.llm = None

    # 新增：快速操作相关状态
    if 'pending_query' not in st.session_state:
        st.session_state.pending_query = None

    if 'query_source' not in st.session_state:
        st.session_state.query_source = None

    # 新增：保存分析结果历史，防止闪退
    if 'analysis_history' not in st.session_state:
        st.session_state.analysis_history = []

    if 'show_latest_result' not in st.session_state:
        st.session_state.show_latest_result = False

    # 元数据管理页面状态
    if 'show_metadata_page' not in st.session_state:
        st.session_state.show_metadata_page = False

def check_api_key():
    """检查API密钥"""
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        st.error("❌ 未找到DASHSCOPE_API_KEY环境变量")
        st.info("请在项目根目录创建.env文件，并添加: DASHSCOPE_API_KEY=your-api-key")
        return False
    return True

def create_sidebar():
    """创建侧边栏"""
    with st.sidebar:
        st.title("🤖 智能数据分析助手")
        st.markdown("---")
        
        # API状态检查
        if check_api_key():
            st.success("✅ API密钥已配置")
        else:
            st.error("❌ API密钥未配置")
            return
        
        # 文件上传区域
        st.subheader("📁 数据文件上传")
        uploaded_file = st.file_uploader(
            "选择数据文件",
            type=['csv', 'xlsx', 'xls', 'txt', 'json'],
            help="支持CSV、Excel、TXT、JSON格式"
        )
        
        if uploaded_file is not None:
            if st.button("上传文件", type="primary"):
                with st.spinner("正在上传文件..."):
                    file_path = FileManager.save_uploaded_file(uploaded_file)
                    if file_path:
                        st.success(f"✅ 文件上传成功: {uploaded_file.name}")
                        st.rerun()
        
        # 已上传文件列表
        st.subheader("📋 已上传文件")
        uploaded_files = FileManager.get_uploaded_files()
        
        if uploaded_files:
            for file_info in uploaded_files:
                col1, col2 = st.columns([3, 1])
                with col1:
                    if st.button(f"📄 {file_info['name']}", key=f"load_{file_info['name']}"):
                        with st.spinner("正在加载数据..."):
                            df = FileManager.load_data_file(file_info['path'])
                            if df is not None:
                                st.session_state.current_data = df
                                st.session_state.current_file = file_info['name']

                                # 自动注册到元数据管理系统
                                try:
                                    # 使用文件名（不含扩展名）作为表格名
                                    table_name = file_info['name']
                                    if '.' in table_name:
                                        table_name = table_name.rsplit('.', 1)[0]

                                    # 检查是否已经注册
                                    existing_metadata = metadata_manager.get_table_metadata(table_name)
                                    if not existing_metadata:
                                        # 自动注册表格到元数据系统
                                        metadata_manager.register_table(table_name, df, use_smart_inference=True)
                                        st.success(f"✅ 数据加载成功，已自动注册到元数据系统")
                                    else:
                                        st.success(f"✅ 数据加载成功，元数据已存在")
                                except Exception as e:
                                    st.success(f"✅ 数据加载成功")
                                    st.warning(f"⚠️ 元数据注册失败: {e}")

                                st.rerun()
                
                with col2:
                    file_size = f"{file_info['size'] / 1024:.1f}KB"
                    st.caption(file_size)
        else:
            st.info("暂无上传文件")
        
        # 元数据管理
        st.markdown("---")
        st.subheader("🎯 元数据管理")

        if st.button("📊 管理元数据", help="配置表格和列的元数据信息"):
            st.session_state.show_metadata_page = True
            st.rerun()

        # 元数据状态显示
        if st.session_state.current_data is not None:
            current_file = getattr(st.session_state, 'current_file', 'unknown')
            # 使用文件名（不含扩展名）作为表格名
            table_name = current_file
            if '.' in table_name:
                table_name = table_name.rsplit('.', 1)[0]

            table_metadata = metadata_manager.get_table_metadata(table_name)

            if table_metadata:
                st.success("✅ 已配置元数据")
                st.caption(f"表格名: {table_name}")
                st.caption(f"业务领域: {table_metadata.business_domain}")
                st.caption(f"列数: {len(table_metadata.columns)}")
            else:
                st.warning("⚠️ 未配置元数据")
                if st.button("🚀 手动生成", help="手动生成元数据配置"):
                    with st.spinner("生成中..."):
                        metadata_manager.register_table(table_name, st.session_state.current_data, use_smart_inference=True)
                        st.success("✅ 元数据已生成")
                        st.rerun()

        # 会话管理
        st.markdown("---")
        st.subheader("💬 会话管理")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("🗑️ 清空历史"):
                st.session_state.chat_history = []
                st.session_state.chat_manager.save_chat_history([])
                st.success("聊天历史已清空")
                st.rerun()

        with col2:
            if st.button("🔄 新会话"):
                st.session_state.session_id = str(uuid.uuid4())
                st.session_state.chat_manager = ChatManager()
                st.session_state.chat_history = []
                st.success("新会话已创建")
                st.rerun()

def display_chat_history():
    """显示聊天历史"""
    if st.session_state.chat_history:
        for message in st.session_state.chat_history:
            if message['role'] == 'user':
                with st.chat_message("user"):
                    st.write(message['content'])
                    if message.get('data_info'):
                        st.caption(f"📊 数据: {message['data_info']}")
            else:
                with st.chat_message("assistant"):
                    st.write(message['content'])

def process_user_query(user_input):
    """处理用户查询"""
    try:
        # 初始化LLM
        if st.session_state.llm is None:
            st.session_state.llm = TongyiQianwenLLM()

        # 创建一个容器来显示结果
        result_container = st.container()

        with result_container:
            # 执行数据分析
            st.info("🔍 正在使用通义千问分析您的问题...")

            try:
                # 获取当前文件名作为表格名称
                current_file = getattr(st.session_state, 'current_file', 'data_table')
                # 使用文件名（不含扩展名）作为表格名
                table_name = current_file
                if '.' in table_name:
                    table_name = table_name.rsplit('.', 1)[0]

                # 使用增强的分析函数，包含错误恢复
                analysis_result = enhanced_analyze_with_fallback(
                    st.session_state.current_data,
                    user_input
                )

                if analysis_result['success']:
                    result = analysis_result['result']
                    if analysis_result.get('fallback_used'):
                        st.info("🔧 使用了备用图表生成方案")
                else:
                    st.error(f"❌ 分析失败: {analysis_result.get('error', '未知错误')}")
                    return

                if result and result.get('success', False):
                    st.success("✅ 分析完成！")

                    # 显示生成的代码
                    if result.get('code'):
                        with st.expander("📝 生成的代码", expanded=False):
                            st.code(result['code'], language='python')

                    # 保存分析结果到历史记录，防止闪退
                    result['timestamp'] = pd.Timestamp.now().strftime("%H:%M:%S")
                    result['query'] = user_input
                    st.session_state.analysis_history.append(result)
                    st.session_state.show_latest_result = True

                    # 限制历史记录数量，避免内存过多占用
                    if len(st.session_state.analysis_history) > 5:
                        st.session_state.analysis_history = st.session_state.analysis_history[-5:]

                    # 使用增强格式化器显示结果
                    if result.get('output'):
                        # 使用容器确保结果稳定显示
                        with st.container():
                            # 直接使用新的格式化器显示结果
                            EnhancedResultFormatter.format_and_display_result(result)

                    # 显示错误信息（如果有警告）
                    if result.get('error') and result['success']:
                        st.warning(f"⚠️ 执行警告: {result['error']}")

                    # 图表显示已移至结果格式化器中的"📊 可视化"部分，避免重复显示

                    # 保存成功的分析结果 - 使用简化的消息而不是原始输出
                    response_msg = f"✅ 通义千问分析完成！\n\n"

                    # 根据输出类型生成简化的描述
                    if result.get('output'):
                        output_type = EnhancedResultFormatter._detect_output_type(result['output'])
                        if output_type == 'dataframe_info':
                            response_msg += "📊 已显示数据集基本信息和列详情"
                        elif output_type == 'statistics_summary':
                            response_msg += "📈 已显示数据统计摘要"
                        elif output_type == 'single_number':
                            response_msg += f"🔢 计算结果: {result['output'].strip()}"
                        elif output_type == 'tabular_data':
                            response_msg += "📋 已显示表格数据"
                        elif output_type == 'series_data':
                            response_msg += "📊 已显示序列数据和可视化"
                        elif output_type == 'correlation_matrix':
                            response_msg += "🎯 已显示相关性分析结果"
                        else:
                            # 对于其他类型，显示简化的输出
                            output_preview = result['output'][:100].replace('\n', ' ')
                            response_msg += f"📄 分析结果: {output_preview}..."

                    if result.get('has_chart'):
                        response_msg += "\n📈 已生成数据可视化图表"

                    st.session_state.chat_manager.add_message("assistant", response_msg)

                    return True

                elif result and not result.get('success', False):
                    # 分析失败
                    st.error("❌ 分析执行失败")

                    if result.get('code'):
                        with st.expander("📝 生成的代码", expanded=True):
                            st.code(result['code'], language='python')

                    if result.get('error'):
                        st.error(f"错误详情: {result['error']}")

                    error_msg = f"❌ 分析失败: {result.get('error', '未知错误')}"
                    st.session_state.chat_manager.add_message("assistant", error_msg)
                    return False

                else:
                    st.warning("⚠️ 分析未返回结果，请尝试重新表述您的问题")
                    return False

            except Exception as analysis_error:
                st.error(f"❌ 分析过程中出现错误: {str(analysis_error)}")

                # 提供一些建议
                st.markdown("### 💡 建议")
                st.markdown("""
                - 请确保您的问题清晰明确
                - 尝试使用更简单的表述
                - 检查数据是否包含相关字段
                - 例如："计算平均值"、"显示前10行"、"按某列排序"
                """)

                error_msg = f"❌ 分析失败: {str(analysis_error)}"
                st.session_state.chat_manager.add_message("assistant", error_msg)
                return False

    except Exception as e:
        error_msg = f"❌ 处理请求时出现错误: {str(e)}"
        st.error(error_msg)
        st.session_state.chat_manager.add_message("assistant", error_msg)
        return False

def create_quick_actions():
    """创建简化的快速操作按钮"""
    # 完全移除快速分析区域，简化界面
    # 用户可以直接在聊天框中输入查询
    pass

def main():
    """主函数"""
    # 初始化
    init_session_state()

    # 检查是否显示元数据管理页面
    if st.session_state.get('show_metadata_page', False):
        # 显示返回按钮
        if st.button("← 返回主页"):
            st.session_state.show_metadata_page = False
            st.rerun()

        # 显示元数据管理界面
        MetadataUI.render_metadata_management()
        return

    # 创建侧边栏
    create_sidebar()

    # 主界面
    st.title("🤖 智能数据分析助手")
    st.markdown("基于通义千问和PandasAI的智能数据分析平台")

    # 检查API密钥
    if not check_api_key():
        st.stop()

    # 当前数据状态 - 简化显示
    if st.session_state.current_data is not None:
        st.success(f"✅ 数据已加载: {getattr(st.session_state, 'current_file', '未知文件')} "
                  f"({st.session_state.current_data.shape[0]}行 × {st.session_state.current_data.shape[1]}列)")

        # 简化的数据预览 - 移除详细的列信息显示
        with st.expander("📊 数据预览", expanded=False):
            st.dataframe(st.session_state.current_data.head(5))

        # 简化的元数据摘要
        current_file = getattr(st.session_state, 'current_file', 'data_table')
        MetadataUI.render_metadata_summary(current_file, st.session_state.current_data)

        # 快速操作（已简化为空函数）
        create_quick_actions()

    else:
        st.info("👈 请先在侧边栏上传数据文件")

        # 显示使用说明
        with st.expander("📖 使用说明", expanded=True):
            st.markdown("""
            ### 🚀 如何使用智能数据分析助手

            1. **上传数据文件**
               - 在左侧边栏点击"选择数据文件"
               - 支持CSV、Excel、TXT、JSON格式
               - 上传后文件会自动保存，下次可直接使用

            2. **开始智能对话**
               - 上传数据后，可以用自然语言提问
               - 例如："计算总销售额"、"哪个产品销量最高？"
               - 支持中文和英文查询

            3. **使用快速分析**
               - 数据加载后会显示快速分析按钮
               - 一键获取数据概览、缺失值检查等

            4. **会话管理**
               - 对话历史会自动保存
               - 可以清空历史或创建新会话

            ### 💡 查询示例
            - "显示前10行数据"
            - "计算各列的平均值"
            - "按销量排序"
            - "找出价格最高的产品"
            - "生成销售趋势图表"
            """)

    # 聊天界面
    st.subheader("💬 智能对话")

    # 显示聊天历史
    display_chat_history()

    # 显示分析结果历史（防止闪退）
    if st.session_state.show_latest_result and st.session_state.analysis_history:
        st.markdown("---")
        st.subheader("📊 分析结果历史")

        # 显示最近的分析结果
        for i, result in enumerate(reversed(st.session_state.analysis_history)):
            with st.expander(f"🔍 {result.get('query', '未知查询')} ({result.get('timestamp', '未知时间')})", expanded=(i==0)):
                # 显示生成的代码（使用details而不是嵌套expander）
                if result.get('code'):
                    st.markdown("**📝 生成的代码:**")
                    st.code(result['code'], language='python')

                # 使用增强格式化器显示结果
                if result.get('output'):
                    with st.container():
                        EnhancedResultFormatter.format_and_display_result(result)

                # 显示错误信息（如果有警告）
                if result.get('error') and result['success']:
                    st.warning(f"⚠️ 执行警告: {result['error']}")

        # 添加清除按钮
        if st.button("🗑️ 清除所有结果", help="清除所有分析结果历史"):
            st.session_state.analysis_history = []
            st.session_state.show_latest_result = False
            st.rerun()

    # 处理快速操作的待处理查询
    if hasattr(st.session_state, 'pending_query') and st.session_state.pending_query:
        query = st.session_state.pending_query
        st.session_state.pending_query = None  # 清除待处理查询

        # 不清除历史结果，保持查询历史

        # 检查是否有数据
        if st.session_state.current_data is None:
            st.error("请先上传数据文件")
        else:
            # 添加用户消息到聊天历史
            st.session_state.chat_manager.add_message(
                "user",
                query,
                f"{getattr(st.session_state, 'current_file', '未知文件')}"
            )

            # 显示用户消息
            with st.chat_message("user"):
                st.write(query)
                st.caption(f"📊 数据: {getattr(st.session_state, 'current_file', '未知文件')}")

            # 处理查询
            with st.chat_message("assistant"):
                with st.spinner("正在分析数据..."):
                    process_user_query(query)
        return

    # 用户输入
    user_input = st.chat_input("请输入您的问题...")

    if user_input:
        # 不清除历史结果，保持查询历史

        # 检查是否有数据
        if st.session_state.current_data is None:
            st.error("请先上传数据文件")
            return

        # 添加用户消息
        st.session_state.chat_manager.add_message(
            "user",
            user_input,
            f"{getattr(st.session_state, 'current_file', '未知文件')}"
        )

        # 显示用户消息
        with st.chat_message("user"):
            st.write(user_input)
            st.caption(f"📊 数据: {getattr(st.session_state, 'current_file', '未知文件')}")

        # 处理用户请求
        with st.chat_message("assistant"):
            with st.spinner("正在分析数据..."):
                process_user_query(user_input)

        st.rerun()

if __name__ == "__main__":
    main()
